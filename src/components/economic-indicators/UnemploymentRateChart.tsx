"use client";

import React from 'react';
import FREDL<PERSON><PERSON>hart from './FREDLineChart';
import { useFREDSeriesObservations } from '@/lib/state';
import { format, subYears } from 'date-fns';

const UnemploymentRateChart: React.FC = () => {
  const endDate = format(new Date(), 'yyyy-MM-dd');
  const startDate = format(subYears(new Date(), 10), 'yyyy-MM-dd');

  // Fetch Unemployment Rate data
  const unemploymentQuery = useFREDSeriesObservations('UNRATE', startDate, endDate);

  const formatUnemploymentValue = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  return (
    <div className="w-full space-y-8 p-6">
      <div className="space-y-8">
        {/* Unemployment Rate Chart */}
        <FREDLineChart
          data={unemploymentQuery.data?.observations || []}
          title="Unemployment Rate"
          isLoading={unemploymentQuery.isLoading}
          color="#F39C12"
          yAxisLabel="Percent"
          formatValue={formatUnemploymentValue}
          height={500}
        />

        {/* Information about Unemployment vs BTC */}
        <div className="bg-gray-800 p-4 rounded-lg">
          <h3 className="text-white text-lg font-semibold mb-2">Unemployment Rate and Economic Impact</h3>
          <p className="text-gray-400 text-sm">
            The unemployment rate is a key indicator of economic health. High unemployment often leads to 
            expansionary monetary policy (money printing) to stimulate the economy. This can drive inflation 
            and currency debasement, making Bitcoin attractive as an alternative store of value. Conversely, 
            low unemployment may lead to tighter monetary policy.
          </p>
        </div>
      </div>
    </div>
  );
};

export default UnemploymentRateChart;
