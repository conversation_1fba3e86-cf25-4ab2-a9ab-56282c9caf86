"use client";

import React from 'react';
import FREDLineChart from './FREDLineChart';
import { useFREDSeriesObservations, useCoinPriceHistory } from '@/lib/state';
import { format, subYears } from 'date-fns';

const M2vsBTCChart: React.FC = () => {
  const endDate = format(new Date(), 'yyyy-MM-dd');
  const startDate = format(subYears(new Date(), 10), 'yyyy-MM-dd');

  // Fetch M2 Money Supply data
  const m2Query = useFREDSeriesObservations('M2SL', startDate, endDate);
  
  // Fetch Bitcoin price data (using 1y period for now)
  const btcQuery = useCoinPriceHistory('Qwsogvtv82FCd', '1y');

  const formatM2Value = (value: number) => {
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(1)}T`;
    }
    return `$${value.toFixed(0)}B`;
  };

  return (
    <div className="w-full space-y-8 p-6">
      <div className="space-y-8">
        {/* M2 Money Supply Chart */}
        <FREDLineChart
          data={m2Query.data?.observations || []}
          title="M2 Money Supply"
          isLoading={m2Query.isLoading}
          color="#FF6B6B"
          yAxisLabel="Billions of Dollars"
          formatValue={formatM2Value}
          height={500}
        />

        {/* Bitcoin Price Chart for comparison */}
        {btcQuery.data?.data.history && (
          <div className="w-full space-y-4">
            <h2 className="text-2xl font-bold text-white">Bitcoin Price (for comparison)</h2>
            <div className="bg-gray-800 p-4 rounded-lg">
              <p className="text-white text-sm">
                Current Bitcoin Price: ${parseFloat(btcQuery.data.data.history[0]?.price || '0').toLocaleString()}
              </p>
              <p className="text-gray-400 text-xs mt-2">
                Note: This shows the relationship between M2 money supply expansion and Bitcoin price over time.
                As M2 increases (money printing), Bitcoin often serves as a hedge against currency debasement.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default M2vsBTCChart;
