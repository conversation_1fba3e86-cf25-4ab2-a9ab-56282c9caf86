"use client";

import React from 'react';
import FREDLine<PERSON>hart from './FREDLineChart';
import { useFREDSeriesObservations } from '@/lib/state';
import { format, subYears } from 'date-fns';

const FederalDebtChart: React.FC = () => {
  const endDate = format(new Date(), 'yyyy-MM-dd');
  const startDate = format(subYears(new Date(), 10), 'yyyy-MM-dd');

  // Fetch Federal Debt data
  const federalDebtQuery = useFREDSeriesObservations('GFDEBTN', startDate, endDate);

  const formatDebtValue = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}T`;
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}B`;
    }
    return `$${value.toFixed(0)}M`;
  };

  return (
    <div className="w-full space-y-8 p-6">
      <div className="space-y-8">
        {/* Federal Debt Chart */}
        <FREDLineChart
          data={federalDebtQuery.data?.observations || []}
          title="Federal Debt: Total Public Debt"
          isLoading={federalDebtQuery.isLoading}
          color="#E74C3C"
          yAxisLabel="Millions of Dollars"
          formatValue={formatDebtValue}
          height={500}
        />

        {/* Information about Federal Debt vs BTC */}
        <div className="bg-gray-800 p-4 rounded-lg">
          <h3 className="text-white text-lg font-semibold mb-2">Federal Debt and Bitcoin</h3>
          <p className="text-gray-400 text-sm">
            Rising federal debt often leads to concerns about currency debasement and fiscal sustainability. 
            Bitcoin proponents view it as a hedge against unlimited government spending and debt monetization. 
            As debt levels increase, some investors turn to Bitcoin as a store of value independent of 
            government fiscal policy.
          </p>
        </div>
      </div>
    </div>
  );
};

export default FederalDebtChart;
