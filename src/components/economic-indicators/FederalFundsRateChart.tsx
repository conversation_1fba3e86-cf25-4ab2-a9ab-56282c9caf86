"use client";

import React from 'react';
import FREDLine<PERSON>hart from './FREDLineChart';
import { useFREDSeriesObservations } from '@/lib/state';
import { format, subYears } from 'date-fns';

const FederalFundsRateChart: React.FC = () => {
  const endDate = format(new Date(), 'yyyy-MM-dd');
  const startDate = format(subYears(new Date(), 10), 'yyyy-MM-dd');

  // Fetch Federal Funds Rate data
  const fedFundsQuery = useFREDSeriesObservations('FEDFUNDS', startDate, endDate);

  const formatFedFundsValue = (value: number) => {
    return `${value.toFixed(2)}%`;
  };

  return (
    <div className="w-full space-y-8 p-6">
      <div className="space-y-8">
        {/* Federal Funds Rate Chart */}
        <FREDLineChart
          data={fedFundsQuery.data?.observations || []}
          title="Federal Funds Effective Rate"
          isLoading={fedFundsQuery.isLoading}
          color="#3498DB"
          yAxisLabel="Percent"
          formatValue={formatFedFundsValue}
          height={500}
        />

        {/* Information about Fed Funds Rate vs BTC */}
        <div className="bg-gray-800 p-4 rounded-lg">
          <h3 className="text-white text-lg font-semibold mb-2">Federal Funds Rate and Bitcoin</h3>
          <p className="text-gray-400 text-sm">
            The Federal Funds Rate is the interest rate at which banks lend to each other overnight. 
            It&apos;s a primary tool of monetary policy. Low rates make borrowing cheaper and can drive 
            investment into riskier assets like Bitcoin. High rates make traditional savings more 
            attractive and can reduce demand for alternative investments. This rate directly influences 
            the cost of capital and investment flows in the economy.
          </p>
        </div>
      </div>
    </div>
  );
};

export default FederalFundsRateChart;
