"use client";

import React from 'react';
import FREDLineChart from './FREDLineChart';
import { useFREDSeriesObservations } from '@/lib/state';
import { format, subYears } from 'date-fns';

const ConsumerIndexvsBTCChart: React.FC = () => {
  const endDate = format(new Date(), 'yyyy-MM-dd');
  const startDate = format(subYears(new Date(), 10), 'yyyy-MM-dd');

  // Fetch Consumer Price Index data
  const cpiQuery = useFREDSeriesObservations('CPIAUCNS', startDate, endDate);

  const formatCPIValue = (value: number) => {
    return value.toFixed(1);
  };

  return (
    <div className="w-full space-y-8 p-6">
      <div className="space-y-8">
        {/* Consumer Price Index Chart */}
        <FREDLineChart
          data={cpiQuery.data?.observations || []}
          title="Consumer Price Index for All Urban Consumers: All Items"
          isLoading={cpiQuery.isLoading}
          color="#4ECDC4"
          yAxisLabel="Index 1982-1984=100"
          formatValue={formatCPIValue}
          height={500}
        />

        {/* Information about CPI vs BTC */}
        <div className="bg-gray-800 p-4 rounded-lg">
          <h3 className="text-white text-lg font-semibold mb-2">CPI vs Bitcoin Relationship</h3>
          <p className="text-gray-400 text-sm">
            The Consumer Price Index (CPI) measures inflation by tracking the cost of goods and services. 
            Bitcoin is often viewed as a hedge against inflation. When CPI rises significantly, it indicates 
            currency debasement, which historically has driven demand for Bitcoin as a store of value.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ConsumerIndexvsBTCChart;
