"use client";

import React from 'react';
import FREDLine<PERSON>hart from './FREDLineChart';
import { useFREDSeriesObservations } from '@/lib/state';
import { format, subYears } from 'date-fns';

const RetailSalesChart: React.FC = () => {
  const endDate = format(new Date(), 'yyyy-MM-dd');
  const startDate = format(subYears(new Date(), 10), 'yyyy-MM-dd');

  // Fetch Retail Sales data
  const retailSalesQuery = useFREDSeriesObservations('RSXFS', startDate, endDate);

  const formatRetailSalesValue = (value: number) => {
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}B`;
    }
    return `$${value.toFixed(0)}M`;
  };

  return (
    <div className="w-full space-y-8 p-6">
      <div className="space-y-8">
        {/* Retail Sales Chart */}
        <FREDLineChart
          data={retailSalesQuery.data?.observations || []}
          title="Advance Retail Sales: Retail Trade"
          isLoading={retailSalesQuery.isLoading}
          color="#E67E22"
          yAxisLabel="Millions of Dollars"
          formatValue={formatRetailSalesValue}
          height={500}
        />

        {/* Information about Retail Sales vs BTC */}
        <div className="bg-gray-800 p-4 rounded-lg">
          <h3 className="text-white text-lg font-semibold mb-2">Retail Sales Economic Impact</h3>
          <p className="text-gray-400 text-sm">
            Retail sales data reflects consumer spending patterns and economic health. 
            Strong retail sales indicate economic growth, which can influence Bitcoin adoption 
            and investment flows. During economic uncertainty, retail sales often decline while 
            alternative investments like Bitcoin may see increased interest.
          </p>
        </div>
      </div>
    </div>
  );
};

export default RetailSalesChart;
