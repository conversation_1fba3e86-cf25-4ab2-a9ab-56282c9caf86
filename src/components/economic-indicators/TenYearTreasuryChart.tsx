"use client";

import React from 'react';
import FREDLine<PERSON>hart from './FREDLineChart';
import { useFREDSeriesObservations } from '@/lib/state';
import { format, subYears } from 'date-fns';

const TenYearTreasuryChart: React.FC = () => {
  const endDate = format(new Date(), 'yyyy-MM-dd');
  const startDate = format(subYears(new Date(), 10), 'yyyy-MM-dd');

  // Fetch 10-Year Treasury Constant Maturity Rate data
  const treasuryQuery = useFREDSeriesObservations('GS10', startDate, endDate);

  const formatTreasuryValue = (value: number) => {
    return `${value.toFixed(2)}%`;
  };

  return (
    <div className="w-full space-y-8 p-6">
      <div className="space-y-8">
        {/* 10-Year Treasury Chart */}
        <FREDLineChart
          data={treasuryQuery.data?.observations || []}
          title="10-Year Treasury Constant Maturity Rate"
          isLoading={treasuryQuery.isLoading}
          color="#9B59B6"
          yAxisLabel="Percent"
          formatValue={formatTreasuryValue}
          height={500}
        />

        {/* Information about Treasury vs BTC */}
        <div className="bg-gray-800 p-4 rounded-lg">
          <h3 className="text-white text-lg font-semibold mb-2">10-Year Treasury vs Bitcoin</h3>
          <p className="text-gray-400 text-sm">
            The 10-Year Treasury yield is a key indicator of economic health and monetary policy. 
            When yields are low, it often drives investors toward alternative assets like Bitcoin. 
            Conversely, rising yields can make traditional bonds more attractive relative to riskier assets.
          </p>
        </div>
      </div>
    </div>
  );
};

export default TenYearTreasuryChart;
