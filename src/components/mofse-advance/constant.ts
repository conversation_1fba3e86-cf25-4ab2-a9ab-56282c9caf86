export const BITCOIN_ICON_URL =
  "https://cdn.coinranking.com/bOabBYkcX/bitcoin_btc.svg";

export const FILTER_LIST = {
  SENDING_ADDRESS: "sending-address",
  RECEIVING_ADDRESS: "receiving-address",
  TRANSACTION_COUNT: "transaction-count",
  TRANSACTION_VOLUME: "transaction-volume",
  ACTIVE_ADDRESS: "active-address",
  UNIQUE_SENDING_WALLET: "unique-sending-wallet",
  UNIQUE_RECEIVING_WALLET: "unique-receiving-wallet",
  TOTAL_UNIQUE_WALLET: "total-unique-wallet",
  NEW_WALLET_CREATED: "new-wallet-created",
  BLOCK_MINED: "block-mined",
  AVG_TRANSACTION_VALUE: "avg-transaction-value",
  TOTAL_FEE: "total-fee",
  WALLET_SENDING_GTE_1: "wallet-sending-gte-1",
  WALLET_SENDING_GTE_10: "wallet-sending-gte-10",
  WALLET_SENDING_GTE_100: "wallet-sending-gte-100",
  COIN_RANKINGS: 'coin-rankings',
  BTCVSGOLDVSSP500: 'bitcoin-vs-gold-vs-sp500',
  TOP_GAINERS: 'top-gainers',
  TOP_LOSERS: 'top-losers',
  STABLE_COINS: 'stable-coins',
  CRYPTOCURRENCIES: 'cryptocurrencies',
  CBDC: 'cbdc',
  CHINA: 'china',
  INDIA: 'india',
  // Economic Indicators
  M2_VS_BTC: 'm2-vs-btc',
  CONSUMER_INDEX_VS_BTC: 'consumer-index-vs-btc',
  TEN_YEAR_TREASURY: 'ten-year-treasury',
  RETAIL_SALES: 'retail-sales',
  FEDERAL_DEBT: 'federal-debt',
  UNEMPLOYMENT_RATE: 'unemployment-rate',
  FEDERAL_FUNDS_RATE: 'federal-funds-rate',
  // Currency Statistics
  MARKET_CAP: "market-cap",
  EXCHANGE_TRADED_VOLUME: "exchange-traded-volume",

  // Block Details
  AVERAGE_TRANSACTIONS_PER_BLOCK: "average-transactions-per-block",
  AVERAGE_CONFIRMATION_TIME: "average-confirmation-time",
  TOTAL_NUMBER_OF_TRANSACTIONS: "total-number-of-transactions",

  // Mining Information
  TOTAL_HASH_RATE: "total-hash-rate",
  NETWORK_DIFFICULTY: "network-difficulty",
  MINERS_REVENUE_USD: "miners-revenue-usd",
  TOTAL_TRANSACTION_FEES_BTC: "total-transaction-fees-btc",
  TOTAL_TRANSACTION_FEES_USD: "total-transaction-fees-usd",
  FEES_PER_TRANSACTION_USD: "fees-per-transaction-usd",
  COST_PER_TRANSACTION: "cost-per-transaction",

  // Network Activity
  UNIQUE_ADDRESSES_USED: "unique-addresses-used",
  MEMPOOL_TRANSACTION_COUNT: "mempool-transaction-count",
  ESTIMATED_TRANSACTION_VALUE_BTC: "estimated-transaction-value-btc",
  ESTIMATED_TRANSACTION_VALUE_USD: "estimated-transaction-value-usd",
  UNSPENT_TRANSACTION_OUTPUTS: "unspent-transaction-outputs",
};
