"use client";
import React from "react";
import { useProfile } from "@/lib/state";
import { useRouter } from "next/navigation";

interface ChartOverlayProps {
  children: React.ReactNode;
  className?: string;
}

const ChartOverlay: React.FC<ChartOverlayProps> = ({ children, className = "" }) => {
  const userQuery = useProfile();
  const user = userQuery.data;
  const router = useRouter();

  const handleLogin = () => {
    router.push("/login");
  };

  const handleSignUp = () => {
    router.push("/sign-up");
  };

  // If user is authenticated, show the chart normally
  if (user) {
    return <div className={className}>{children}</div>;
  }

  // If user is not authenticated, show the overlay
  return (
    <div className={`relative ${className}`}>
      {/* Render the chart with reduced opacity and blur */}
      <div className="opacity-20 pointer-events-none blur-sm">
        {children}
      </div>

      {/* Overlay */}
      <div className="absolute inset-0 flex flex-col items-center justify-center bg-white/20 backdrop-blur-md">
        <div className="text-center space-y-4 p-6 rounded-lg bg-white/95 backdrop-blur-sm shadow-lg max-w-md mx-4">
          <h3 className="text-lg font-semibold text-gray-900">
            These metrics require an upgrade to a standard subscription. However, you’re currently on a free trial. Please sign in to view the metrics.
          </h3>
          {/* <p className="text-sm text-gray-600">
            Compare all plan features{" "}
            <span className="text-blue-600 underline cursor-pointer">here</span>
          </p> */}

          <div className="flex gap-3 justify-center mt-6">
            <button
              onClick={handleLogin}
              className="px-6 py-2 text-sm border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 rounded-md transition-colors cursor-pointer"
            >
              Log in
            </button>
            <button
              onClick={handleSignUp}
              className="px-6 py-2 text-sm bg-bg-primary text-white  rounded-md transition-colors cursor-pointer"
            >
              Sign up
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChartOverlay;
