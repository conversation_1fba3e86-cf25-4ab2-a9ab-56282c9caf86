import React from 'react'
import { HeroSectionChart } from '../home/<USER>'
import ChartOverlay from '../comman/ChartOverlay'

export default function BitcoinGoldSpCharts() {
  return (
    <div className="w-full space-y-8 p-6">
      <HeroSectionChart/>

      {/* Economic Indicators Charts */}
      <div className="space-y-8">

        {/* 10-Year Treasury Constant Maturity Minus 2-Year Treasury Constant Maturity */}
        <div className="w-full">
          <h2 className="text-2xl font-bold mb-4 text-white">10-Year Treasury Constant Maturity Minus 2-Year Treasury Constant Maturity</h2>
          <ChartOverlay className="w-full">
            <iframe
              src="https://fred.stlouisfed.org/graph/graph-landing.php?g=1KDcp"
              style={{overflow: 'hidden', width: '100%', height: '600px', border: 'none'}}
              loading="lazy"
            />
          </ChartOverlay>
        </div>

        {/* 10-Year Treasury Yield */}
        <div className="w-full">
          <h2 className="text-2xl font-bold mb-4 text-white">10-Year Treasury Yield</h2>
          <ChartOverlay className="w-full">
            <iframe
              src="https://fred.stlouisfed.org/graph/graph-landing.php?g=1KDeF"
              style={{overflow: 'hidden', width: '100%', height: '600px', border: 'none'}}
              loading="lazy"
            />
          </ChartOverlay>
        </div>

        {/* 2-Year Treasury Yield */}
        <div className="w-full">
          <h2 className="text-2xl font-bold mb-4 text-white">2-Year Treasury Yield</h2>
          <ChartOverlay className="w-full">
            <iframe
              src="https://fred.stlouisfed.org/graph/graph-landing.php?g=1KDeW"
              style={{overflow: 'hidden', width: '100%', height: '600px', border: 'none'}}
              loading="lazy"
            />
          </ChartOverlay>
        </div>

        {/* Consumer Price Index */}
        <div className="w-full">
          <h2 className="text-2xl font-bold mb-4 text-white">Consumer Price Index</h2>
          <ChartOverlay className="w-full">
            <iframe
              src="https://fred.stlouisfed.org/graph/graph-landing.php?g=1KDeY"
              style={{overflow: 'hidden', width: '100%', height: '600px', border: 'none'}}
              loading="lazy"
            />
          </ChartOverlay>
        </div>

        {/* Federal Debt */}
        <div className="w-full">
          <h2 className="text-2xl font-bold mb-4 text-white">Federal Debt</h2>
          <ChartOverlay className="w-full">
            <iframe
              src="https://fred.stlouisfed.org/graph/graph-landing.php?g=1JqiD"
              style={{overflow: 'hidden', width: '100%', height: '600px', border: 'none'}}
              loading="lazy"
            />
          </ChartOverlay>
        </div>

        {/* Federal Funds Rate */}
        <div className="w-full">
          <h2 className="text-2xl font-bold mb-4 text-white">Federal Funds Rate</h2>
          <ChartOverlay className="w-full">
            <iframe
              src="https://fred.stlouisfed.org/graph/graph-landing.php?g=1JYwD"
              style={{overflow: 'hidden', width: '100%', height: '600px', border: 'none'}}
              loading="lazy"
            />
          </ChartOverlay>
        </div>

        {/* Government Bond Yields */}
        <div className="w-full">
          <h2 className="text-2xl font-bold mb-4 text-white">Government Bond Yields</h2>
          <ChartOverlay className="w-full">
            <iframe
              src="https://fred.stlouisfed.org/graph/graph-landing.php?g=1KDeF"
              style={{overflow: 'hidden', width: '100%', height: '600px', border: 'none'}}
              loading="lazy"
            />
          </ChartOverlay>
        </div>

        {/* Gross Domestic Product */}
        <div className="w-full">
          <h2 className="text-2xl font-bold mb-4 text-white">Gross Domestic Product</h2>
          <ChartOverlay className="w-full">
            <iframe
              src="https://fred.stlouisfed.org/graph/graph-landing.php?g=1JV0m"
              style={{overflow: 'hidden', width: '100%', height: '600px', border: 'none'}}
              loading="lazy"
            />
          </ChartOverlay>
        </div>

      </div>
    </div>
  )
}