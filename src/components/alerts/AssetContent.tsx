import { Coin } from "@/lib/api.interface";
import USDThresholdChart from "./USDThresholdChart";
import { FILTER_LIST } from "../mofse-advance/constant";
// import { HeroSectionChart } from "../home/<USER>";
import type { ComponentType } from 'react';
import { CryptoTable } from "./CryptocurrenciesTable";
import { StableCoinTable } from "./StableCoinsTable";
import BitcoinGoldSpCharts from "./BitcoinGoldSpCharts";
import { TopGainersTable } from "./TopGainersTable";
import { TopLosersTable } from "./TopLosersTable";

interface AssetContentProps {
  coin: Coin;
  filter: string;
}

const chartComponentMap: Record<string, ComponentType<any>> = {
  'bitcoin-vs-gold-vs-sp500': BitcoinGoldSpCharts,
  'cryptocurrencies': CryptoTable,
  'stable-coins': StableCoinTable,
  'top-gainers': TopGainersTable,
  'top-losers': TopLosersTable,
};

const AssetContent: React.FC<AssetContentProps> = ({ coin, filter }) => {

    function getHeading(filter: string) {
  switch (filter) {
    case FILTER_LIST.TRANSACTION_COUNT:
      return "Transaction Count";
    case FILTER_LIST.TRANSACTION_VOLUME:
      return "Transaction Volume";
    case FILTER_LIST.UNIQUE_SENDING_WALLET:
      return "Unique Sending Wallet";
    case FILTER_LIST.UNIQUE_RECEIVING_WALLET:
      return "Unique Receiving Wallet";
    case FILTER_LIST.TOTAL_UNIQUE_WALLET:
      return "Total Unique Wallet";
    case FILTER_LIST.NEW_WALLET_CREATED:
      return "New Wallet Created";
    case FILTER_LIST.BLOCK_MINED:
      return "Block Mined";
    case FILTER_LIST.AVG_TRANSACTION_VALUE:
      return "Average Transaction Value";
    case FILTER_LIST.TOTAL_FEE:
      return "Total Fee";
    case FILTER_LIST.WALLET_SENDING_GTE_1:
      return "Wallet Sending > 1 " + coin?.symbol;
    case FILTER_LIST.WALLET_SENDING_GTE_10:
      return "Wallet Sending > 10 " + coin?.symbol;
    case FILTER_LIST.WALLET_SENDING_GTE_100:
      return "Wallet Sending > 100 " + coin?.symbol;
    case FILTER_LIST.COIN_RANKINGS: 
      return "Coin Rankings";
    case FILTER_LIST.BTCVSGOLDVSSP500:
      return "BTC vs Gold vs S&P 500";
    case FILTER_LIST.TOP_GAINERS: 
      return "Top Gainers";
    case FILTER_LIST.TOP_LOSERS:
      return "Top Losers";
    default:
      return "";
  }
}

const ChartToRender = chartComponentMap[filter];

  return (
    <div className="flex">
      <div className="flex-1 py-6 pl-6 overflow-x-auto">
       {['cryptocurrencies'].includes(filter) ? null :
        <div className="flex items-center gap-4 mb-6">
          <h2 className="text-lg font-medium text-gray-900">
            {['top-gainers', 'top-losers'].includes(filter) ? getHeading(filter) : `${coin.symbol}: ${getHeading(filter)}`}
          </h2>
        </div>}
        <div className="space-y-6">
            {/* {filter === 'bitcoin-vs-gold-vs-sp500' ? <HeroSectionChart/>: 
          <USDThresholdChart coin={coin} onChainFilter={filter} />} */}
          {ChartToRender ? (
            <ChartToRender />
          ) : (
            <USDThresholdChart coin={coin} onChainFilter={filter} />
          )}
        </div>
      </div>
    </div>
  );
};

export default AssetContent;
