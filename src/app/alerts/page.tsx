import Header from "@/components/comman/Header";
import React from "react";
import { Metadata } from "next";
import Footer from "@/components/comman/Footer";
import Alerts from "@/components/alerts/Alerts";

import { api } from "@/lib/api";
// import ProtectedRoute from "@/components/comman/ProtectedRoute";

export const metadata: Metadata = {
  title: "MOFSE - Alerts",
  description: "Coming Soon",
};

const Page = async () => {
   "use client";
  const coins = await api.coins.getAll("marketCap", 0, 10, "DESC", "");
  const allCoins = coins.data.coins;
  const coinList = allCoins.filter((coin) =>
    ["BTC", "ETH", "BNB", "SOL"].includes(coin.symbol)
  );

  return (
    <>
      <Header />
      <p className="text-center text-gray-900">
       Note: We&apos;re updating our dataset weekly with fresh data points. Check back soon to explore the latest additions!
      </p>
      <div className="center w-full">
        <Alerts coins={coinList} />
      </div>
      {/* <ComingSoon /> */}
      <Footer />
    </>
  );
};

export default Page;
